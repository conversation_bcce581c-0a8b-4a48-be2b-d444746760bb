import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ApiConfig, AppSettings, GeminiModel, SettingsState } from '@/types';
import { createGeminiService, getGeminiService } from '@/services/gemini';

interface SettingsStore extends SettingsState {
  // Actions
  updateApiConfig: (config: Partial<ApiConfig>) => void;
  updateAppSettings: (settings: Partial<AppSettings>) => void;
  validateApiKey: (apiKey: string) => Promise<boolean>;
  loadModels: () => Promise<void>;
  resetSettings: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// 默认 API 配置
const DEFAULT_API_CONFIG: ApiConfig = {
  apiKey: '',
  apiUrl: 'https://generativelanguage.googleapis.com/v1beta',
  model: 'gemini-2.5-flash',
  temperature: 0.7,
  maxTokens: 8192,
};

// 默认应用设置
const DEFAULT_APP_SETTINGS: AppSettings = {
  theme: 'light',
  language: 'zh-CN',
  fontSize: 'medium',
  autoSave: true,
  notifications: true,
};

// 默认模型列表
const DEFAULT_MODELS: GeminiModel[] = [
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    description: '最强大的思维模型，具有最高响应准确性和最先进的性能',
    maxTokens: 1048576,
    supportFiles: true,
  },
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    description: '在价格性能方面最佳的模型，提供全面的功能',
    maxTokens: 1048576,
    supportFiles: true,
  },
  {
    id: 'gemini-2.5-flash-lite',
    name: 'Gemini 2.5 Flash-Lite',
    description: '针对成本效率和低延迟优化的模型',
    maxTokens: 1048576,
    supportFiles: true,
  },
  {
    id: 'gemini-2.0-flash',
    name: 'Gemini 2.0 Flash',
    description: '下一代功能和改进的能力，包括卓越的速度',
    maxTokens: 1048576,
    supportFiles: true,
  },
  {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    description: '针对广泛推理任务优化的中型多模态模型（已弃用）',
    maxTokens: 2097152,
    supportFiles: true,
  },
  {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    description: '快速且多功能的多模态模型（已弃用）',
    maxTokens: 1048576,
    supportFiles: true,
  },
];

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      // Initial state
      apiConfig: DEFAULT_API_CONFIG,
      appSettings: DEFAULT_APP_SETTINGS,
      models: DEFAULT_MODELS,
      loading: false,
      error: null,

      // Actions
      updateApiConfig: (config: Partial<ApiConfig>) => {
        const newConfig = { ...get().apiConfig, ...config };
        set({ apiConfig: newConfig });

        // 如果 API Key 发生变化，重新创建服务实例
        if (config.apiKey || config.model || config.temperature || config.maxTokens) {
          try {
            createGeminiService({
              apiKey: newConfig.apiKey,
              model: newConfig.model,
              temperature: newConfig.temperature,
              maxTokens: newConfig.maxTokens,
            });
          } catch (error) {
            console.error('Failed to create Gemini service:', error);
          }
        }
      },

      updateAppSettings: (settings: Partial<AppSettings>) => {
        set((state) => ({
          appSettings: { ...state.appSettings, ...settings },
        }));
      },

      validateApiKey: async (apiKey: string) => {
        set({ loading: true, error: null });

        try {
          // 创建临时服务实例进行验证
          const tempService = createGeminiService({
            apiKey,
            model: get().apiConfig.model,
          });

          const isValid = await tempService.validateApiKey(apiKey);
          
          if (isValid) {
            // 验证成功，更新配置
            get().updateApiConfig({ apiKey });
            set({ loading: false });
            return true;
          } else {
            set({ 
              loading: false, 
              error: 'API Key 无效，请检查后重试' 
            });
            return false;
          }
        } catch (error) {
          set({ 
            loading: false, 
            error: error instanceof Error ? error.message : 'API Key 验证失败' 
          });
          return false;
        }
      },

      loadModels: async () => {
        const { apiConfig } = get();
        
        if (!apiConfig.apiKey) {
          set({ error: '请先配置 API Key' });
          return;
        }

        set({ loading: true, error: null });

        try {
          const service = getGeminiService();
          if (!service) {
            throw new Error('Gemini service not initialized');
          }

          const result = await service.getModels();
          
          if (result.success && result.data) {
            // 处理 API 返回的模型数据
            const models: GeminiModel[] = result.data.map((model: Record<string, unknown>) => ({
              id: (model.name as string)?.split('/').pop() || (model.name as string),
              name: (model.displayName as string) || (model.name as string),
              description: (model.description as string) || '官方模型',
              maxTokens: (model.inputTokenLimit as number) || 8192,
              supportFiles: (model.supportedGenerationMethods as string[])?.includes('generateContent') || false,
            }));

            set({ 
              models: models.length > 0 ? models : DEFAULT_MODELS,
              loading: false 
            });
          } else {
            // 如果 API 调用失败，使用默认模型列表
            set({ 
              models: DEFAULT_MODELS,
              loading: false,
              error: result.error || '加载模型列表失败，使用默认列表'
            });
          }
        } catch (error) {
          set({ 
            models: DEFAULT_MODELS,
            loading: false, 
            error: error instanceof Error ? error.message : '加载模型列表失败' 
          });
        }
      },

      resetSettings: () => {
        set({
          apiConfig: DEFAULT_API_CONFIG,
          appSettings: DEFAULT_APP_SETTINGS,
          models: DEFAULT_MODELS,
          loading: false,
          error: null,
        });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },
    }),
    {
      name: 'settings-storage',
      partialize: (state) => ({
        apiConfig: state.apiConfig,
        appSettings: state.appSettings,
      }),
    }
  )
);

// 初始化 Gemini 服务
export const initializeGeminiService = () => {
  const { apiConfig } = useSettingsStore.getState();
  
  if (apiConfig.apiKey) {
    try {
      createGeminiService({
        apiKey: apiConfig.apiKey,
        model: apiConfig.model,
        temperature: apiConfig.temperature,
        maxTokens: apiConfig.maxTokens,
      });
    } catch (error) {
      console.error('Failed to initialize Gemini service:', error);
    }
  }
};
