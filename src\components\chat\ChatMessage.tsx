'use client';

import { memo } from 'react';
import { createStyles } from 'antd-style';
import { Flexbox } from 'react-layout-kit';
import { User, Bo<PERSON>, Co<PERSON>, RotateCcw, RefreshCw, Download, FileText, Trash2 } from 'lucide-react';
import { ActionIcon } from '@lobehub/ui';
import { Card, message as antdMessage } from 'antd';
import { Message } from '@/types';
import { useChatStore, DEFAULT_AGENT_ID } from '@/store/chat';
import Image from 'next/image';
// 简单的时间格式化函数
const formatTime = (date: Date | string | number): string => {
  const now = new Date();
  const targetDate = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return '刚刚';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}小时前`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}天前`;
  }

  // 超过一周显示具体日期
  return targetDate.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const useStyles = createStyles(({ css, token }) => ({
  messageContainer: css`
    display: flex;
    gap: 12px;
    padding: 16px 0;
    width: 100%;

    &:hover {
      .message-actions {
        opacity: 1;
      }
    }

    &.user-message {
      flex-direction: row-reverse;
      justify-content: flex-start;
    }

    &.assistant-message {
      justify-content: flex-start;
    }
  `,
  
  avatar: css`
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;

    &.user {
      background: ${token.colorPrimary};
      color: white;
    }

    &.assistant {
      background: ${token.colorFillQuaternary};
      color: ${token.colorTextSecondary};
    }
  `,

  userAvatar: css`
    width: 100%;
    height: 100%;
    object-fit: cover;
  `,

  userLetterAvatar: css`
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    flex-shrink: 0;
  `,

  agentAvatar: css`
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  `,
  
  messageContent: css`
    flex: 0 1 auto;
    min-width: 0;
    max-width: 70%;
  `,

  userMessageContent: css`
    flex: 0 1 auto;
    min-width: 0;
    max-width: 70%;
    text-align: left;
  `,

  messageCard: css`
    margin: 0;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .ant-card-body {
      padding: 12px 16px;
    }
  `,

  userMessageCard: css`
    margin: 0;
    border-radius: 12px;
    background: ${token.colorPrimary};
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .ant-card-body {
      padding: 12px 16px;
    }
  `,

  assistantMessageCard: css`
    margin: 0;
    border-radius: 12px;
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .ant-card-body {
      padding: 12px 16px;
    }
  `,
  
  messageHeader: css`
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  `,

  userMessageHeader: css`
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    justify-content: flex-end;
    flex-direction: row;

    .ant-typography, span {
      color: rgba(255, 255, 255, 0.8) !important;
    }
  `,
  
  senderName: css`
    font-size: 14px;
    font-weight: 600;
    color: ${token.colorText};
  `,

  userSenderName: css`
    font-size: 14px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
  `,

  timestamp: css`
    font-size: 12px;
    color: ${token.colorTextTertiary};
  `,

  userTimestamp: css`
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  `,
  
  messageText: css`
    font-size: 14px;
    line-height: 1.6;
    color: ${token.colorText};
    white-space: pre-wrap;
    word-break: break-word;
    margin: 0;
  `,

  userMessageText: css`
    font-size: 14px;
    line-height: 1.6;
    color: white;
    white-space: pre-wrap;
    word-break: break-word;
    margin: 0;
  `,
  
  messageActions: css`
    opacity: 0;
    transition: opacity 0.2s ease;
    display: flex;
    gap: 4px;
  `,
  
  errorMessage: css`
    color: ${token.colorError};
    background: ${token.colorErrorBg};
    border: 1px solid ${token.colorErrorBorder};
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
  `,
}));

interface ChatMessageProps {
  message: Message;
  index: number;
}

const ChatMessage = memo<ChatMessageProps>(({ message, index }) => {
  const { styles } = useStyles();
  const { currentSessions, activeSessionId, sendMessage, deleteMessage, regenerateMessage } = useChatStore();

  const handleCopy = () => {
    navigator.clipboard.writeText(message.content);
    antdMessage.success('已复制到剪贴板');
  };

  const handleRegenerate = () => {
    if (regenerateMessage) {
      regenerateMessage(message.id);
    }
  };

  const handleResubmit = () => {
    if (sendMessage) {
      sendMessage(message.content, message.files);
    }
  };

  const handleDelete = () => {
    if (deleteMessage) {
      deleteMessage(message.id);
      antdMessage.success('消息已删除');
    }
  };

  const handleExportTxt = () => {
    const blob = new Blob([message.content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `message_${new Date().getTime()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    antdMessage.success('TXT文件已下载');
  };

  const handleExportDocx = async () => {
    try {
      // 创建简单的HTML内容
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Message Export</title>
        </head>
        <body>
          <p>${message.content.replace(/\n/g, '</p><p>')}</p>
        </body>
        </html>
      `;

      const blob = new Blob([htmlContent], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `message_${new Date().getTime()}.docx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      antdMessage.success('DOCX文件已下载');
    } catch (error) {
      antdMessage.error('导出失败');
    }
  };

  const isUser = message.role === 'user';
  const isError = message.status === 'error';

  // 获取当前活跃会话
  const currentSession = currentSessions.find(s => s.id === activeSessionId);

  // 确定AI回复的显示名称
  const getAIDisplayName = () => {
    // 如果当前会话有智能体且不是默认智能体
    if (currentSession?.agentId &&
        currentSession.agentId !== DEFAULT_AGENT_ID &&
        currentSession.agentName) {
      return currentSession.agentName;
    }
    // 否则显示默认名称
    return '微甜AI Studio';
  };

  // 渲染用户头像（字母头像）
  const renderUserAvatar = () => {
    // 获取用户名首字母，默认为 "U"
    const userLetter = '你'.charAt(0).toUpperCase();
    return (
      <div className={styles.userLetterAvatar}>
        {userLetter}
      </div>
    );
  };

  // 渲染智能体头像（favicon.ico）
  const renderAgentAvatar = () => {
    return (
      <div className={`${styles.avatar} assistant`}>
        <Image
          src="/favicon.ico"
          alt="AI Assistant"
          width={32}
          height={32}
          className={styles.agentAvatar}
        />
      </div>
    );
  };

  return (
    <div className={`${styles.messageContainer} ${isUser ? 'user-message' : 'assistant-message'}`}>
      {isUser ? renderUserAvatar() : renderAgentAvatar()}

      <div className={isUser ? styles.userMessageContent : styles.messageContent}>
        <Card
          className={isUser ? styles.userMessageCard : styles.assistantMessageCard}
          size="small"
        >
          <div className={isUser ? styles.userMessageHeader : styles.messageHeader}>
            {isUser ? (
              <>
                <span className={styles.userTimestamp}>
                  {formatTime(message.timestamp)}
                </span>
                <span className={styles.userSenderName}>
                  你
                </span>
              </>
            ) : (
              <>
                <span className={styles.senderName}>
                  {getAIDisplayName()}
                </span>
                <span className={styles.timestamp}>
                  {formatTime(message.timestamp)}
                </span>
              </>
            )}
          </div>

          <div className={isError ? styles.errorMessage : (isUser ? styles.userMessageText : styles.messageText)}>
            {message.content}
          </div>

          {/* 用户消息按钮 */}
          {isUser && (
            <div className={`message-actions ${styles.messageActions}`}>
              <ActionIcon
                icon={Copy}
                size={{ blockSize: 28, size: 14 }}
                onClick={handleCopy}
                title="复制"
              />
              <ActionIcon
                icon={RefreshCw}
                size={{ blockSize: 28, size: 14 }}
                onClick={handleResubmit}
                title="重新提交"
              />
              <ActionIcon
                icon={Trash2}
                size={{ blockSize: 28, size: 14 }}
                onClick={handleDelete}
                title="删除"
              />
            </div>
          )}

          {/* AI回复按钮 */}
          {!isUser && !isError && (
            <div className={`message-actions ${styles.messageActions}`}>
              <ActionIcon
                icon={Copy}
                size={{ blockSize: 28, size: 14 }}
                onClick={handleCopy}
                title="复制"
              />
              <ActionIcon
                icon={RotateCcw}
                size={{ blockSize: 28, size: 14 }}
                onClick={handleRegenerate}
                title="重新生成"
              />
              <ActionIcon
                icon={FileText}
                size={{ blockSize: 28, size: 14 }}
                onClick={handleExportTxt}
                title="导出TXT"
              />
              <ActionIcon
                icon={Download}
                size={{ blockSize: 28, size: 14 }}
                onClick={handleExportDocx}
                title="导出DOCX"
              />
              <ActionIcon
                icon={Trash2}
                size={{ blockSize: 28, size: 14 }}
                onClick={handleDelete}
                title="删除"
              />
            </div>
          )}
        </Card>
      </div>
    </div>
  );
});

export default ChatMessage;
