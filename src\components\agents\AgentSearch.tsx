'use client';

import React from 'react';
import { Input } from 'antd';
import { Search } from 'lucide-react';
import { createStyles } from 'antd-style';
import { useAgentStore } from '@/store/agent';

const useStyles = createStyles(({ css, token }) => ({
  searchContainer: css`
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    padding: 16px;
    background: ${token.colorBgContainer};
    border-radius: 8px;
    border: 1px solid ${token.colorBorderSecondary};
  `,
  searchInput: css`
    flex: 1;
  `,
}));

interface AgentSearchProps {
}

const AgentSearch: React.FC<AgentSearchProps> = () => {
  const { styles } = useStyles();
  const { searchQuery, setSearchQuery } = useAgentStore();

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  return (
    <div className={styles.searchContainer}>
      <Input
        className={styles.searchInput}
        placeholder="搜索智能体名称、描述或标签..."
        prefix={<Search size={16} />}
        value={searchQuery}
        onChange={handleSearchChange}
        allowClear
      />
    </div>
  );
};

export default AgentSearch;
