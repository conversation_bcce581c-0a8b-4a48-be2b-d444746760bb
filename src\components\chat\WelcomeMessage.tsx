'use client';

import { memo } from 'react';
import { createStyles } from 'antd-style';
import { Flexbox } from 'react-layout-kit';
import { MessageSquare, FileText, Code, Globe, Bot } from 'lucide-react';
import Image from 'next/image';
import { useChatStore, DEFAULT_AGENT_ID } from '@/store/chat';
import { Tag } from 'antd';

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  `,
  
  icon: css`
    width: 64px;
    height: 64px;
    border-radius: 16px;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
  `,
  
  title: css`
    font-size: 28px;
    font-weight: 600;
    color: ${token.colorText};
    margin-bottom: 12px;
  `,

  agentTag: css`
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  `,
  
  subtitle: css`
    font-size: 16px;
    color: ${token.colorTextSecondary};
    margin-bottom: 40px;
    line-height: 1.5;
  `,
  
  suggestions: css`
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    width: 100%;
    max-width: 500px;
  `,
  
  suggestion: css`
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 12px;
    background: ${token.colorBgContainer};
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    
    &:hover {
      border-color: ${token.colorPrimary};
      background: ${token.colorPrimaryBg};
      transform: translateY(-2px);
      box-shadow: 0 4px 12px ${token.colorPrimary}20;
    }
  `,
  
  suggestionIcon: css`
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: ${token.colorFillQuaternary};
    display: flex;
    align-items: center;
    justify-content: center;
    color: ${token.colorPrimary};
    flex-shrink: 0;
  `,
  
  suggestionText: css`
    font-size: 14px;
    font-weight: 500;
    color: ${token.colorText};
    line-height: 1.4;
  `,
}));

const WelcomeMessage = memo(() => {
  const { styles } = useStyles();
  const { currentSessions, activeSessionId } = useChatStore();

  // 获取当前活跃会话
  const currentSession = currentSessions.find(s => s.id === activeSessionId);

  const suggestions = [
    {
      icon: MessageSquare,
      text: '开始一段对话',
    },
    {
      icon: FileText,
      text: '帮我写一篇文章',
    },
    {
      icon: Code,
      text: '解释这段代码',
    },
    {
      icon: Globe,
      text: '翻译这段文字',
    },
  ];

  const handleSuggestionClick = (text: string) => {
    // TODO: 实现建议点击
    console.log('建议点击:', text);
  };

  // 检查是否是智能体对话（排除普通对话）
  const isAgentChat = currentSession?.agentId &&
                      currentSession?.agentId !== DEFAULT_AGENT_ID &&
                      currentSession?.agentName;

  return (
    <div className={styles.container}>
      <div className={styles.icon}>
        <Image
          src="/favicon.ico"
          alt="AI Assistant"
          width={64}
          height={64}
          style={{ borderRadius: '16px' }}
        />
      </div>

      {/* 智能体标签 */}
      {isAgentChat && (
        <div className={styles.agentTag}>
          <Tag
            icon={<Bot size={14} />}
            color="blue"
            style={{ fontSize: '14px', padding: '4px 12px' }}
          >
            智能体对话
          </Tag>
        </div>
      )}

      <h1 className={styles.title}>
        {isAgentChat ? `你好！我是 ${currentSession.agentName}` : '你好！我是微甜Al Studio'}
      </h1>

      <p className={styles.subtitle}>
        {isAgentChat
          ? (currentSession.agentDescription || '我是您的专属智能体助手，随时为您提供帮助！')
          : '欢迎来到微甜Al Studio，这里有很多好玩实用的影视广告AI工具，助力你的影视学习工作！请问现在能为你做什么？'
        }
      </p>
      
      <div className={styles.suggestions}>
        {suggestions.map((suggestion, index) => (
          <div
            key={index}
            className={styles.suggestion}
            onClick={() => handleSuggestionClick(suggestion.text)}
          >
            <div className={styles.suggestionIcon}>
              <suggestion.icon size={16} />
            </div>
            <span className={styles.suggestionText}>
              {suggestion.text}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
});

export default WelcomeMessage;
