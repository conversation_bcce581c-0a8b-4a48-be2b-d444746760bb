import { AIProvider, FileInfo } from '@/types';
import { useAIProviderConfigsStore } from '@/store/aiProviderConfigs';
import { Agent } from '@/types/agent';

interface ConversationMessage {
  role: 'user' | 'assistant';
  content: string;
}

interface AIServiceResponse {
  success: boolean;
  data?: string;
  error?: string;
}

interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
}

interface CozeConfig {
  apiKey: string;
  botId: string;
  userId: string;
}

class AIService {
  private getProviderConfig(provider: AIProvider): ProviderConfig | null {
    try {
      // 使用新的数据库配置系统
      const store = useAIProviderConfigsStore.getState();
      const providerConfig = store.getProviderConfig(provider);

      if (!providerConfig?.enabled || !providerConfig?.apiKey) {
        return null;
      }

      return {
        apiKey: providerConfig.apiKey,
        baseUrl: providerConfig.baseUrl,
        model: providerConfig.defaultModel,
        temperature: providerConfig.temperature,
        maxTokens: providerConfig.maxTokens,
      };
    } catch (error) {
      console.error('Failed to get provider config:', error);
      return null;
    }
  }

  async sendMessage(
    provider: AIProvider,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[],
    agent?: Agent
  ): Promise<AIServiceResponse> {
    // 如果提供了智能体信息，使用Coze API
    if (agent && agent.cozeConfig && agent.cozeConfig.apiKey && agent.cozeConfig.botId) {
      return await this.callCozeAPI(agent.cozeConfig, content, conversationHistory);
    }

    const config = this.getProviderConfig(provider);

    if (!config) {
      return {
        success: false,
        error: `${provider} 服务未配置或未启用，请先在设置中配置 API Key`,
      };
    }

    try {
      switch (provider) {
        case 'google':
          return await this.callGoogleAPI(config, content, files, conversationHistory);
        case 'openai':
          return await this.callOpenAIAPI(config, content, files, conversationHistory);
        case 'anthropic':
          return await this.callAnthropicAPI(config, content, files, conversationHistory);
        case 'deepseek':
          return await this.callDeepSeekAPI(config, content, files, conversationHistory);
        default:
          return {
            success: false,
            error: `不支持的 AI 提供商: ${provider}`,
          };
      }
    } catch (error) {
      console.error(`${provider} API call failed:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  private async callGoogleAPI(
    config: ProviderConfig,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    const url = `${config.baseUrl || 'https://generativelanguage.googleapis.com'}/v1beta/models/${config.model}:generateContent?key=${config.apiKey}`;

    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    const requestBody = {
      contents: messages.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      })),
      generationConfig: {
        temperature: config.temperature,
        maxOutputTokens: config.maxTokens,
      }
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const responseText = data.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!responseText) {
      throw new Error('API 返回了空响应');
    }

    return {
      success: true,
      data: responseText,
    };
  }

  private async callOpenAIAPI(
    config: ProviderConfig,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    const url = `${config.baseUrl || 'https://api.openai.com'}/v1/chat/completions`;

    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    const requestBody = {
      model: config.model,
      messages: messages,
      temperature: config.temperature,
      max_tokens: config.maxTokens,
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const responseText = data.choices?.[0]?.message?.content;

    if (!responseText) {
      throw new Error('API 返回了空响应');
    }

    return {
      success: true,
      data: responseText,
    };
  }

  private async callAnthropicAPI(
    config: ProviderConfig,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    const url = `${config.baseUrl || 'https://api.anthropic.com'}/v1/messages`;

    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    const requestBody = {
      model: config.model,
      messages: messages,
      max_tokens: config.maxTokens,
      temperature: config.temperature,
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const responseText = data.content?.[0]?.text;

    if (!responseText) {
      throw new Error('API 返回了空响应');
    }

    return {
      success: true,
      data: responseText,
    };
  }

  private async callDeepSeekAPI(
    config: ProviderConfig,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    const url = `${config.baseUrl || 'https://api.deepseek.com'}/v1/chat/completions`;

    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    const requestBody = {
      model: config.model,
      messages: messages,
      temperature: config.temperature,
      max_tokens: config.maxTokens,
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const responseText = data.choices?.[0]?.message?.content;

    if (!responseText) {
      throw new Error('API 返回了空响应');
    }

    return {
      success: true,
      data: responseText,
    };
  }

  private async callCozeAPI(
    config: CozeConfig,
    content: string,
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    const url = 'https://api.coze.cn/open_api/v2/chat';

    // 构建消息历史
    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    const requestBody = {
      conversation_id: `conv_${Date.now()}`,
      bot_id: config.botId,
      user: config.userId,
      query: content,
      chat_history: messages.slice(0, -1).map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content,
        content_type: 'text'
      })),
      stream: false
    };

    console.log('Coze API Request:', {
      url,
      botId: config.botId,
      userId: config.userId,
      query: content
    });

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Coze API Error:', errorData);
      throw new Error(errorData.msg || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Coze API Response:', data);

    // 检查响应格式
    if (data.code !== 0) {
      throw new Error(data.msg || 'Coze API 返回错误');
    }

    // 提取回复内容
    const messages_response = data.messages || [];
    const assistantMessage = messages_response.find((msg: any) =>
      msg.role === 'assistant' && msg.type === 'answer'
    );

    if (!assistantMessage || !assistantMessage.content) {
      throw new Error('Coze API 返回了空响应');
    }

    return {
      success: true,
      data: assistantMessage.content,
    };
  }
}

export const aiService = new AIService();
export default aiService;
