import React, { useState, useEffect } from 'react';
import { Card, Input, Button, Switch, Select, Space, Typography, Divider, Tag, Tooltip, Alert, App } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone, SettingOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { AIProvider, AI_PROVIDERS } from '@/types/ai-provider';
import { useAIProviderConfigsStore } from '@/store/aiProviderConfigs';
import { useAuthStore } from '@/store/auth';

const { Title, Text } = Typography;
const { Option } = Select;

interface ProviderConfigProps {
  provider: AIProvider;
}

export const ProviderConfigComponent: React.FC<ProviderConfigProps> = ({ provider }) => {
  const { message } = App.useApp();
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [localConfig, setLocalConfig] = useState({
    enabled: false,
    apiKey: '',
    baseUrl: '',
    defaultModel: '',
    temperature: 0.7,
    maxTokens: 4096,
  });

  const { user } = useAuthStore();
  const {
    loading,
    error,
    loadConfigs,
    saveConfig,
    deleteConfig,
    getProviderConfig,
    clearError,
  } = useAIProviderConfigsStore();

  const providerInfo = AI_PROVIDERS[provider];
  const currentConfig = getProviderConfig(provider);

  // 加载配置
  useEffect(() => {
    if (user?.id) {
      loadConfigs(user.id);
    }
  }, [user?.id, loadConfigs]);

  // 更新本地配置
  useEffect(() => {
    if (currentConfig) {
      setLocalConfig({
        enabled: currentConfig.enabled,
        apiKey: currentConfig.apiKey,
        baseUrl: currentConfig.baseUrl || providerInfo.defaultBaseUrl,
        defaultModel: currentConfig.defaultModel || (providerInfo.models[0]?.id || ''),
        temperature: currentConfig.temperature,
        maxTokens: currentConfig.maxTokens,
      });
    } else {
      setLocalConfig({
        enabled: false,
        apiKey: '',
        baseUrl: providerInfo.defaultBaseUrl,
        defaultModel: providerInfo.models[0]?.id || '',
        temperature: 0.7,
        maxTokens: 4096,
      });
    }
  }, [currentConfig, providerInfo]);

  const handleSave = async () => {
    if (!user?.id) {
      message.error('请先登录');
      return;
    }

    try {
      await saveConfig({
        userId: user.id,
        provider,
        enabled: localConfig.enabled,
        apiKey: localConfig.apiKey,
        baseUrl: localConfig.baseUrl,
        defaultModel: localConfig.defaultModel,
        temperature: localConfig.temperature,
        maxTokens: localConfig.maxTokens,
      });
      message.success('配置保存成功');
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    }
  };

  const handleReset = async () => {
    if (!user?.id) {
      message.error('请先登录');
      return;
    }

    try {
      await deleteConfig(user.id, provider);
      message.success('配置重置成功');
    } catch (error) {
      console.error('重置配置失败:', error);
      message.error('重置配置失败');
    }
  };

  const handleTest = async () => {
    if (!localConfig.apiKey) {
      message.error('请先输入 API Key');
      return;
    }

    setTesting(true);
    setTestResult(null);
    setErrorMessage('');

    try {
      // 这里可以添加实际的API测试逻辑
      // 暂时模拟测试
      await new Promise(resolve => setTimeout(resolve, 2000));
      setTestResult('success');
      message.success('连接测试成功');
    } catch (error) {
      setTestResult('error');
      setErrorMessage('连接测试失败');
      message.error('连接测试失败');
    } finally {
      setTesting(false);
    }
  };

  const updateLocalConfig = (key: string, value: any) => {
    setLocalConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  return (
    <Card
      title={
        <Space>
          <span style={{ fontSize: '18px' }}>{providerInfo.icon}</span>
          <div>
            <Title level={4} style={{ margin: 0 }}>
              {providerInfo.name}
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {providerInfo.description}
            </Text>
          </div>
        </Space>
      }
      extra={
        <Switch
          checked={localConfig.enabled}
          onChange={(checked) => updateLocalConfig('enabled', checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      }
      style={{ marginBottom: 16 }}
      loading={loading}
    >
      {error && (
        <Alert
          message={error}
          type="error"
          closable
          onClose={clearError}
          style={{ marginBottom: 16 }}
        />
      )}

      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {/* API Key */}
        <div>
          <Text strong>API Key</Text>
          <Input.Password
            placeholder={`请输入${providerInfo.name} API Key`}
            value={localConfig.apiKey}
            onChange={(e) => updateLocalConfig('apiKey', e.target.value)}
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            style={{ marginTop: 4 }}
          />
        </div>

        {/* API URL */}
        <div>
          <Text strong>API URL</Text>
          <Input
            placeholder={`${providerInfo.name} API 地址`}
            value={localConfig.baseUrl}
            onChange={(e) => updateLocalConfig('baseUrl', e.target.value)}
            style={{ marginTop: 4 }}
          />
        </div>

        {/* 默认模型 */}
        <div>
          <Text strong>默认模型</Text>
          <Select
            style={{ width: '100%', marginTop: 4 }}
            value={localConfig.defaultModel}
            onChange={(value) => updateLocalConfig('defaultModel', value)}
            placeholder="选择默认模型"
          >
            {providerInfo.models.map((model) => (
              <Option key={model.id} value={model.id}>
                <Space>
                  <span>{model.name}</span>
                  <Tag color="blue">
                    {model.maxTokens.toLocaleString()} tokens
                  </Tag>
                </Space>
              </Option>
            ))}
          </Select>
        </div>

        <Divider />

        {/* 操作按钮 */}
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Button
            type="primary"
            ghost
            icon={<SettingOutlined />}
            onClick={handleTest}
            loading={testing}
            disabled={!localConfig.apiKey}
          >
            测试连接
          </Button>
          <Space>
            <Button onClick={handleReset}>
              重置配置
            </Button>
            <Button type="primary" onClick={handleSave}>
              保存配置
            </Button>
          </Space>
        </Space>

        {/* 测试结果 */}
        {testResult && (
          <Alert
            message={testResult === 'success' ? '连接测试成功' : errorMessage}
            type={testResult === 'success' ? 'success' : 'error'}
            icon={testResult === 'success' ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
            showIcon
          />
        )}

        {/* 可用模型 */}
        <div>
          <Text strong>可用模型</Text>
          <div style={{ marginTop: 8 }}>
            <Space wrap>
              {providerInfo.models.map((model) => (
                <Tooltip
                  key={model.id}
                  title={
                    <div>
                      <div>{model.description}</div>
                      <div>最大令牌: {model.maxTokens.toLocaleString()}</div>
                      <div>输入价格: ${model.inputPrice}/1K tokens</div>
                      <div>输出价格: ${model.outputPrice}/1K tokens</div>
                    </div>
                  }
                >
                  <Tag color="blue" style={{ cursor: 'pointer' }}>
                    {model.name} 📎
                  </Tag>
                </Tooltip>
              ))}
            </Space>
          </div>
        </div>
      </Space>
    </Card>
  );
};
