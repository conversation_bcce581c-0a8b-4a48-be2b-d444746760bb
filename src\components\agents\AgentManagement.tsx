'use client';

import React, { useState, useEffect } from 'react';
import { createStyles } from 'antd-style';
import {
  Table,
  Button,
  Switch,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Card,
  Divider,
  Popconfirm,
  App,
  Typography
} from 'antd';
import {
  Plus,
  Edit,
  Trash2,
  DollarSign,
  Clock,
  Hash
} from 'lucide-react';
import { Agent, AgentCategory, AGENT_CATEGORIES, AgentPricingPlan } from '@/types/agent';
import { agentsApi } from '@/lib/api';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    padding: 0;
  `,
  
  header: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  `,
  
  agentCard: css`
    margin-bottom: 16px;
    
    .ant-card-head {
      border-bottom: 1px solid ${token.colorBorderSecondary};
    }
  `,
  
  pricingSection: css`
    margin-top: 16px;
    
    .pricing-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }
  `,
  
  pricingCard: css`
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    
    .pricing-type {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }
    
    .pricing-details {
      color: ${token.colorTextSecondary};
      font-size: 14px;
    }
    
    .pricing-actions {
      margin-top: 8px;
      display: flex;
      gap: 8px;
    }
  `,
  
  modalForm: css`
    .ant-form-item {
      margin-bottom: 16px;
    }
  `,
}));

interface AgentManagementProps {}

const AgentManagement: React.FC<AgentManagementProps> = () => {
  const { styles } = useStyles();
  const { message } = App.useApp();
  
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [selectedAgentId, setSelectedAgentId] = useState<string>('');
  const [pricingPlans, setPricingPlans] = useState<AgentPricingPlan[]>([]);
  
  const [form] = Form.useForm();
  const [pricingForm] = Form.useForm();

  // 加载智能体列表
  const loadAgents = async () => {
    setLoading(true);
    try {
      const response = await agentsApi.getAll();
      if (response.success && response.data) {
        setAgents(response.data);
      } else {
        message.error(response.error || '加载智能体失败');
      }
    } catch (error) {
      message.error('加载智能体失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载定价方案
  const loadPricingPlans = async (agentId: string) => {
    try {
      const response = await fetch(`/api/agent-pricing?agentId=${agentId}`);
      const result = await response.json();
      if (result.success) {
        setPricingPlans(result.data || []);
      }
    } catch (error) {
      console.error('加载定价方案失败:', error);
    }
  };

  useEffect(() => {
    loadAgents();
  }, []);

  // 处理智能体启用/禁用
  const handleToggleEnabled = async (agent: Agent, enabled: boolean) => {
    try {
      const response = await agentsApi.update(agent.id, { enabled });
      if (response.success) {
        message.success(enabled ? '智能体已启用' : '智能体已禁用');
        loadAgents();
      } else {
        message.error(response.error || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 处理添加智能体
  const handleAddAgent = () => {
    setEditingAgent(null);
    form.resetFields();
    setShowAddModal(true);
  };

  // 处理编辑智能体
  const handleEditAgent = (agent: Agent) => {
    setEditingAgent(agent);
    form.setFieldsValue({
      name: agent.name,
      description: agent.description,
      category: agent.category,
      cozeApiKey: agent.cozeConfig?.apiKey,
      cozeBotId: agent.cozeConfig?.botId,
      cozeUserId: agent.cozeConfig?.userId,
      trialUsageCount: agent.trialUsageCount || 3,
    });
    setShowAddModal(true);
  };

  // 处理删除智能体
  const handleDeleteAgent = async (agentId: string) => {
    try {
      const response = await agentsApi.delete(agentId);
      if (response.success) {
        message.success('智能体删除成功');
        loadAgents();
      } else {
        message.error(response.error || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 处理保存智能体
  const handleSaveAgent = async (values: any) => {
    try {
      const agentData = {
        name: values.name,
        description: values.description,
        category: values.category,
        cozeConfig: {
          apiKey: values.cozeApiKey || '',
          botId: values.cozeBotId || '',
          userId: values.cozeUserId || '',
        },
        trialUsageCount: values.trialUsageCount || 3,
      };

      let response;
      if (editingAgent) {
        response = await agentsApi.update(editingAgent.id, agentData);
      } else {
        response = await agentsApi.create(agentData);
      }

      if (response.success) {
        message.success(editingAgent ? '智能体更新成功' : '智能体创建成功');
        setShowAddModal(false);
        loadAgents();
      } else {
        message.error(response.error || '保存失败');
      }
    } catch (error) {
      message.error('保存失败');
    }
  };

  // 处理定价管理
  const handleManagePricing = (agentId: string) => {
    setSelectedAgentId(agentId);
    loadPricingPlans(agentId);
    setShowPricingModal(true);
  };

  // 处理添加定价方案
  const handleAddPricingPlan = async (values: any) => {
    try {
      const response = await fetch('/api/agent-pricing', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agentId: selectedAgentId,
          ...values,
        }),
      });

      const result = await response.json();
      if (result.success) {
        message.success('定价方案创建成功');
        pricingForm.resetFields();
        loadPricingPlans(selectedAgentId);
      } else {
        message.error(result.error || '创建失败');
      }
    } catch (error) {
      message.error('创建失败');
    }
  };

  // 处理删除定价方案
  const handleDeletePricingPlan = async (planId: string) => {
    try {
      const response = await fetch(`/api/agent-pricing?id=${planId}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      if (result.success) {
        message.success('定价方案删除成功');
        loadPricingPlans(selectedAgentId);
      } else {
        message.error(result.error || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  const columns = [
    {
      title: '智能体名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Agent) => (
        <Space>
          <img src="/favicon.ico" alt="" style={{ width: 20, height: 20 }} />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category: AgentCategory) => {
        const categoryInfo = AGENT_CATEGORIES.find(c => c.key === category);
        return <Tag color="blue">{categoryInfo?.label || category}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled: boolean, record: Agent) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleToggleEnabled(record, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '使用次数',
      dataIndex: 'usageCount',
      key: 'usageCount',
      render: (count: number) => count || 0,
    },
    {
      title: '试用次数',
      dataIndex: 'trialUsageCount',
      key: 'trialUsageCount',
      render: (count: number) => (
        <Tag color="blue">{count || 3} 次</Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Agent) => (
        <Space>
          <Button
            type="text"
            icon={<Edit size={16} />}
            onClick={() => handleEditAgent(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={<DollarSign size={16} />}
            onClick={() => handleManagePricing(record.id)}
          >
            定价
          </Button>
          <Popconfirm
            title="确定要删除这个智能体吗？"
            onConfirm={() => handleDeleteAgent(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<Trash2 size={16} />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <Title level={3}>智能体管理</Title>
          <Text type="secondary">管理智能体和定价方案</Text>
        </div>
        <Button
          type="primary"
          icon={<Plus size={16} />}
          onClick={handleAddAgent}
        >
          添加智能体
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={agents}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个智能体`,
        }}
      />

      {/* 添加/编辑智能体模态框 */}
      <Modal
        title={editingAgent ? '编辑智能体' : '添加智能体'}
        open={showAddModal}
        onCancel={() => setShowAddModal(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveAgent}
          className={styles.modalForm}
        >
          <Form.Item
            name="name"
            label="智能体名称"
            rules={[{ required: true, message: '请输入智能体名称' }]}
          >
            <Input placeholder="请输入智能体名称" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea placeholder="请输入智能体描述" rows={3} />
          </Form.Item>

          <Form.Item
            name="category"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类">
              {AGENT_CATEGORIES.map(category => (
                <Option key={category.key} value={category.key}>
                  {category.label} - {category.description}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Divider>Coze 配置</Divider>

          <Form.Item name="cozeApiKey" label="Coze API Key">
            <Input.Password placeholder="请输入 Coze API Key" />
          </Form.Item>

          <Form.Item name="cozeBotId" label="Bot ID">
            <Input placeholder="请输入 Bot ID" />
          </Form.Item>

          <Form.Item name="cozeUserId" label="User ID">
            <Input placeholder="请输入 User ID" />
          </Form.Item>

          <Form.Item
            name="trialUsageCount"
            label="试用次数"
            rules={[
              { required: true, message: '请输入试用次数' },
              { type: 'number', min: 0, max: 100, message: '试用次数必须在0-100之间' }
            ]}
            tooltip="用户在没有付费订阅的情况下可以免费试用该智能体的次数"
          >
            <InputNumber
              placeholder="请输入试用次数"
              min={0}
              max={100}
              style={{ width: '100%' }}
            />
          </Form.Item>



          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingAgent ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setShowAddModal(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 定价管理模态框 */}
      <Modal
        title="定价方案管理"
        open={showPricingModal}
        onCancel={() => setShowPricingModal(false)}
        footer={null}
        width={800}
      >
        <div className={styles.pricingSection}>
          <div className="pricing-header">
            <Title level={4}>现有定价方案</Title>
          </div>

          {pricingPlans.map((plan) => (
            <div key={plan.id} className={styles.pricingCard}>
              <div className="pricing-type">
                {plan.type === 'per_usage' ? (
                  <>
                    <Hash size={16} />
                    <Text strong>按次收费</Text>
                    <Tag color="green">{plan.name}</Tag>
                  </>
                ) : (
                  <>
                    <Clock size={16} />
                    <Text strong>按时计费</Text>
                    <Tag color="blue">{plan.name}</Tag>
                  </>
                )}
              </div>

              <div className="pricing-details">
                {plan.type === 'per_usage' ? (
                  <Text>
                    {plan.usageCount} 次使用，每次 ¥{plan.pricePerUsage}
                  </Text>
                ) : (
                  <Text>
                    {plan.durationDays} 天，总价 ¥{plan.pricePerPeriod}
                  </Text>
                )}
                {plan.description && (
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary">{plan.description}</Text>
                  </div>
                )}
              </div>

              <div className="pricing-actions">
                <Popconfirm
                  title="确定要删除这个定价方案吗？"
                  onConfirm={() => handleDeletePricingPlan(plan.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button size="small" danger icon={<Trash2 size={14} />}>
                    删除
                  </Button>
                </Popconfirm>
              </div>
            </div>
          ))}

          <Divider>添加新定价方案</Divider>

          <Form
            form={pricingForm}
            layout="vertical"
            onFinish={handleAddPricingPlan}
          >
            <Form.Item
              name="name"
              label="方案名称"
              rules={[{ required: true, message: '请输入方案名称' }]}
            >
              <Input placeholder="例如：基础套餐、专业版等" />
            </Form.Item>

            <Form.Item
              name="type"
              label="计费类型"
              rules={[{ required: true, message: '请选择计费类型' }]}
            >
              <Select placeholder="请选择计费类型" onChange={() => pricingForm.resetFields(['usageCount', 'pricePerUsage', 'durationDays', 'pricePerPeriod'])}>
                <Option value="per_usage">按次收费</Option>
                <Option value="time_based">按时计费</Option>
              </Select>
            </Form.Item>

            <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}>
              {({ getFieldValue }) => {
                const type = getFieldValue('type');

                if (type === 'per_usage') {
                  return (
                    <>
                      <Form.Item
                        name="usageCount"
                        label="使用次数"
                        rules={[{ required: true, message: '请输入使用次数' }]}
                      >
                        <InputNumber
                          min={1}
                          placeholder="可使用次数"
                          style={{ width: '100%' }}
                          addonAfter="次"
                        />
                      </Form.Item>

                      <Form.Item
                        name="pricePerUsage"
                        label="单次价格"
                        rules={[{ required: true, message: '请输入单次价格' }]}
                      >
                        <InputNumber
                          min={0}
                          step={0.01}
                          placeholder="每次使用的价格"
                          style={{ width: '100%' }}
                          addonBefore="¥"
                        />
                      </Form.Item>
                    </>
                  );
                }

                if (type === 'time_based') {
                  return (
                    <>
                      <Form.Item
                        name="durationDays"
                        label="有效期"
                        rules={[{ required: true, message: '请输入有效期' }]}
                      >
                        <InputNumber
                          min={1}
                          placeholder="有效天数"
                          style={{ width: '100%' }}
                          addonAfter="天"
                        />
                      </Form.Item>

                      <Form.Item
                        name="pricePerPeriod"
                        label="总价格"
                        rules={[{ required: true, message: '请输入总价格' }]}
                      >
                        <InputNumber
                          min={0}
                          step={0.01}
                          placeholder="整个期间的价格"
                          style={{ width: '100%' }}
                          addonBefore="¥"
                        />
                      </Form.Item>
                    </>
                  );
                }

                return null;
              }}
            </Form.Item>

            <Form.Item name="description" label="方案描述">
              <TextArea placeholder="请输入方案描述" rows={2} />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  添加方案
                </Button>
                <Button onClick={() => pricingForm.resetFields()}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </div>
  );
};

export default AgentManagement;
