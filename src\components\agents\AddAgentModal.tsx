'use client';

import React, { useState } from 'react';
import { Modal, Form, Input, InputNumber, Select, Button, App, Upload, Tag, Space } from 'antd';
import { Plus, Upload as UploadIcon, X } from 'lucide-react';
import { useAgentStore } from '@/store/agent';
import { CreateAgentForm, AGENT_CATEGORIES } from '@/types/agent';

const { TextArea } = Input;
const { Option } = Select;

interface AddAgentModalProps {
  visible: boolean;
  onClose: () => void;
}

const AddAgentModal: React.FC<AddAgentModalProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [presetPrompts, setPresetPrompts] = useState<string[]>([]);
  const [newPrompt, setNewPrompt] = useState('');
  const [avatarUrl, setAvatarUrl] = useState<string>('');
  const { addAgent } = useAgentStore();
  const { message } = App.useApp();

  const handleSubmit = async (values: CreateAgentForm) => {
    setLoading(true);
    try {
      const agentData = {
        ...values,
        avatar: avatarUrl,
        presetPrompts: presetPrompts.filter(prompt => prompt.trim() !== ''),
      };
      await addAgent(agentData);
      message.success('智能体添加成功！');
      handleCancel();
    } catch (error) {
      message.error('添加智能体失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setPresetPrompts([]);
    setNewPrompt('');
    setAvatarUrl('');
    onClose();
  };

  // 添加预设提示词
  const addPresetPrompt = () => {
    if (newPrompt.trim() && !presetPrompts.includes(newPrompt.trim())) {
      setPresetPrompts([...presetPrompts, newPrompt.trim()]);
      setNewPrompt('');
    }
  };

  // 删除预设提示词
  const removePresetPrompt = (index: number) => {
    setPresetPrompts(presetPrompts.filter((_, i) => i !== index));
  };

  // 处理图标上传
  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'done') {
      // 这里应该是上传成功后的URL，暂时使用本地URL
      setAvatarUrl(info.file.response?.url || '/favicon.ico');
      message.success('图标上传成功');
    } else if (info.file.status === 'error') {
      message.error('图标上传失败');
    }
  };

  // 自定义上传函数（暂时模拟）
  const customUpload = ({ file, onSuccess, onError }: any) => {
    // 这里应该实现真实的文件上传逻辑
    // 暂时模拟上传成功
    setTimeout(() => {
      onSuccess({ url: '/favicon.ico' });
    }, 1000);
  };

  return (
    <Modal
      title="添加智能体"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          category: 'general',
          trialUsageCount: 3,
        }}
      >
        <Form.Item
          name="name"
          label="智能体名称"
          rules={[
            { required: true, message: '请输入智能体名称' },
            { max: 50, message: '名称不能超过50个字符' }
          ]}
        >
          <Input placeholder="请输入智能体名称" />
        </Form.Item>

        <Form.Item
          name="description"
          label="智能体描述"
          rules={[
            { max: 200, message: '描述不能超过200个字符' }
          ]}
        >
          <TextArea
            placeholder="请输入智能体描述（可选）"
            rows={3}
            showCount
            maxLength={200}
          />
        </Form.Item>

        <Form.Item
          label="智能体图标"
          tooltip="上传智能体的头像图标，支持 JPG、PNG 格式，建议尺寸 64x64 像素"
        >
          <Upload
            name="avatar"
            listType="picture-card"
            className="avatar-uploader"
            showUploadList={false}
            customRequest={customUpload}
            onChange={handleAvatarChange}
            accept="image/*"
          >
            {avatarUrl ? (
              <img src={avatarUrl} alt="avatar" style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
            ) : (
              <div>
                <UploadIcon size={16} />
                <div style={{ marginTop: 8 }}>上传图标</div>
              </div>
            )}
          </Upload>
        </Form.Item>

        <Form.Item
          name="category"
          label="智能体分类"
          rules={[{ required: true, message: '请选择智能体分类' }]}
        >
          <Select placeholder="请选择智能体分类">
            {AGENT_CATEGORIES.map(category => (
              <Option key={category.key} value={category.key}>
                {category.label} - {category.description}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="预设提示词"
          tooltip="设置一些常用的提示词，用户可以快速点击发送，最多设置8个"
        >
          <div style={{ marginBottom: 8 }}>
            <Space.Compact style={{ width: '100%' }}>
              <Input
                placeholder="输入预设提示词"
                value={newPrompt}
                onChange={(e) => setNewPrompt(e.target.value)}
                onPressEnter={addPresetPrompt}
                maxLength={50}
              />
              <Button
                type="primary"
                onClick={addPresetPrompt}
                disabled={!newPrompt.trim() || presetPrompts.length >= 8}
              >
                添加
              </Button>
            </Space.Compact>
          </div>
          <div style={{ minHeight: 32 }}>
            {presetPrompts.map((prompt, index) => (
              <Tag
                key={index}
                closable
                onClose={() => removePresetPrompt(index)}
                style={{ marginBottom: 4 }}
              >
                {prompt}
              </Tag>
            ))}
            {presetPrompts.length === 0 && (
              <span style={{ color: '#999', fontSize: '12px' }}>
                暂无预设提示词，添加后将在对话界面显示
              </span>
            )}
          </div>
        </Form.Item>

        <Form.Item
          name="cozeApiKey"
          label="Coze API Key"
          rules={[{ required: true, message: '请输入 Coze API Key' }]}
        >
          <Input.Password placeholder="请输入 Coze API Key" />
        </Form.Item>

        <Form.Item
          name="cozeBotId"
          label="Bot ID"
          rules={[{ required: true, message: '请输入 Bot ID' }]}
        >
          <Input placeholder="请输入 Coze Bot ID" />
        </Form.Item>

        <Form.Item
          name="cozeUserId"
          label="User ID"
          rules={[{ required: true, message: '请输入 User ID' }]}
        >
          <Input placeholder="请输入 Coze User ID" />
        </Form.Item>

        <Form.Item
          name="trialUsageCount"
          label="试用次数"
          rules={[
            { required: true, message: '请输入试用次数' },
            { type: 'number', min: 0, max: 100, message: '试用次数必须在0-100之间' }
          ]}
          tooltip="用户在没有付费订阅的情况下可以免费试用该智能体的次数"
        >
          <InputNumber
            placeholder="请输入试用次数"
            min={0}
            max={100}
            style={{ width: '100%' }}
          />
        </Form.Item>



        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Button onClick={handleCancel} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={<Plus size={16} />}
          >
            添加智能体
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddAgentModal;
