'use client';

import React, { useState } from 'react';
import { Modal, Form, Input, InputNumber, Select, Button, App } from 'antd';
import { Plus } from 'lucide-react';
import { useAgentStore } from '@/store/agent';
import { CreateAgentForm, AGENT_CATEGORIES } from '@/types/agent';

const { TextArea } = Input;
const { Option } = Select;

interface AddAgentModalProps {
  visible: boolean;
  onClose: () => void;
}

const AddAgentModal: React.FC<AddAgentModalProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { addAgent } = useAgentStore();
  const { message } = App.useApp();

  const handleSubmit = async (values: CreateAgentForm) => {
    setLoading(true);
    try {
      await addAgent(values);
      message.success('智能体添加成功！');
      form.resetFields();
      onClose();
    } catch (error) {
      message.error('添加智能体失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title="添加智能体"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          category: 'general',
          trialUsageCount: 3,
        }}
      >
        <Form.Item
          name="name"
          label="智能体名称"
          rules={[
            { required: true, message: '请输入智能体名称' },
            { max: 50, message: '名称不能超过50个字符' }
          ]}
        >
          <Input placeholder="请输入智能体名称" />
        </Form.Item>

        <Form.Item
          name="description"
          label="智能体描述"
          rules={[
            { max: 200, message: '描述不能超过200个字符' }
          ]}
        >
          <TextArea
            placeholder="请输入智能体描述（可选）"
            rows={3}
            showCount
            maxLength={200}
          />
        </Form.Item>

        <Form.Item
          name="category"
          label="智能体分类"
          rules={[{ required: true, message: '请选择智能体分类' }]}
        >
          <Select placeholder="请选择智能体分类">
            {AGENT_CATEGORIES.map(category => (
              <Option key={category.key} value={category.key}>
                {category.label} - {category.description}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="cozeApiKey"
          label="Coze API Key"
          rules={[{ required: true, message: '请输入 Coze API Key' }]}
        >
          <Input.Password placeholder="请输入 Coze API Key" />
        </Form.Item>

        <Form.Item
          name="cozeBotId"
          label="Bot ID"
          rules={[{ required: true, message: '请输入 Bot ID' }]}
        >
          <Input placeholder="请输入 Coze Bot ID" />
        </Form.Item>

        <Form.Item
          name="cozeUserId"
          label="User ID"
          rules={[{ required: true, message: '请输入 User ID' }]}
        >
          <Input placeholder="请输入 Coze User ID" />
        </Form.Item>

        <Form.Item
          name="trialUsageCount"
          label="试用次数"
          rules={[
            { required: true, message: '请输入试用次数' },
            { type: 'number', min: 0, max: 100, message: '试用次数必须在0-100之间' }
          ]}
          tooltip="用户在没有付费订阅的情况下可以免费试用该智能体的次数"
        >
          <InputNumber
            placeholder="请输入试用次数"
            min={0}
            max={100}
            style={{ width: '100%' }}
          />
        </Form.Item>



        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Button onClick={handleCancel} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={<Plus size={16} />}
          >
            添加智能体
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddAgentModal;
