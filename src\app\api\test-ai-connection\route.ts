import { NextRequest, NextResponse } from 'next/server';
import { AIProvider } from '@/types';

// 测试AI提供商连接
export async function POST(request: NextRequest) {
  try {
    const { provider, apiKey, baseUrl, model } = await request.json();

    if (!provider || !apiKey) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 根据不同的AI提供商调用相应的API
    const result = await testProviderConnection(provider, apiKey, baseUrl, model);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('AI连接测试失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '连接测试失败' 
      },
      { status: 500 }
    );
  }
}

async function testProviderConnection(
  provider: AIProvider, 
  apiKey: string, 
  baseUrl?: string, 
  model?: string
): Promise<{ success: boolean; error?: string; data?: any }> {
  try {
    switch (provider) {
      case 'openai':
        return await testOpenAIConnection(apiKey, baseUrl, model);
      case 'anthropic':
        return await testAnthropicConnection(apiKey, baseUrl, model);
      case 'google':
        return await testGoogleConnection(apiKey, baseUrl, model);
      case 'deepseek':
        return await testDeepSeekConnection(apiKey, baseUrl, model);
      default:
        return { success: false, error: '不支持的AI提供商' };
    }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '连接测试失败' 
    };
  }
}

async function testOpenAIConnection(apiKey: string, baseUrl?: string, model?: string) {
  const url = `${baseUrl || 'https://api.openai.com'}/v1/models`;

  const response = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorData.message || errorMessage;
    } catch {
      errorMessage = await response.text() || errorMessage;
    }
    throw new Error(`OpenAI API错误: ${errorMessage}`);
  }

  const data = await response.json();
  return {
    success: true,
    data: {
      message: 'OpenAI连接成功',
      modelsCount: data.data?.length || 0
    }
  };
}

async function testAnthropicConnection(apiKey: string, baseUrl?: string, model?: string) {
  const url = `${baseUrl || 'https://api.anthropic.com'}/v1/messages`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01',
    },
    body: JSON.stringify({
      model: model || 'claude-3-haiku-20240307',
      max_tokens: 10,
      messages: [{ role: 'user', content: 'Hi' }],
    }),
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorData.message || errorMessage;
    } catch {
      errorMessage = await response.text() || errorMessage;
    }
    throw new Error(`Anthropic API错误: ${errorMessage}`);
  }

  return {
    success: true,
    data: { message: 'Anthropic连接成功' }
  };
}

async function testGoogleConnection(apiKey: string, baseUrl?: string, model?: string) {
  const url = `${baseUrl || 'https://generativelanguage.googleapis.com'}/v1beta/models/${model || 'gemini-2.0-flash'}:generateContent?key=${apiKey}`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      contents: [{
        parts: [{ text: 'Hi' }]
      }],
      generationConfig: {
        maxOutputTokens: 10,
      },
    }),
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorData.message || errorMessage;
    } catch {
      errorMessage = await response.text() || errorMessage;
    }
    throw new Error(`Google API错误: ${errorMessage}`);
  }

  return {
    success: true,
    data: { message: 'Google Gemini连接成功' }
  };
}

async function testDeepSeekConnection(apiKey: string, baseUrl?: string, model?: string) {
  const url = `${baseUrl || 'https://api.deepseek.com'}/v1/chat/completions`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: model || 'deepseek-chat',
      messages: [{ role: 'user', content: 'Hi' }],
      max_tokens: 10,
    }),
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorData.message || errorMessage;
    } catch {
      errorMessage = await response.text() || errorMessage;
    }
    throw new Error(`DeepSeek API错误: ${errorMessage}`);
  }

  return {
    success: true,
    data: { message: 'DeepSeek连接成功' }
  };
}
