{"name": "story-ai-studio", "version": "1.0.0", "description": "微甜 AI Studio - 智能对话与创作平台", "private": true, "author": "Story AI Team", "license": "MIT", "homepage": "https://story-ai.com", "repository": {"type": "git", "url": "https://github.com/your-org/story-ai-studio.git"}, "keywords": ["ai", "chat", "nextjs", "typescript", "mysql", "authentication"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "echo \"No tests specified\" && exit 0", "setup-db": "mysql -u root -ppassword < scripts/init-db.sql", "db:init": "mysql -u root -ppassword -e \"CREATE DATABASE IF NOT EXISTS story_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\" && mysql -u root -ppassword story_ai < scripts/init-db.sql", "db:reset": "mysql -u root -ppassword -e \"DROP DATABASE IF EXISTS story_ai; CREATE DATABASE story_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\" && npm run setup-db", "postinstall": "echo \"Installation complete. Run 'npm run db:init' to setup database.\"", "clean": "rm -rf .next out dist", "analyze": "cross-env ANALYZE=true npm run build"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/nextjs-registry": "^1.0.1", "@lobehub/ui": "^2.7.5", "@types/nodemailer": "^6.4.17", "ahooks": "^3.9.0", "antd": "^5.26.6", "antd-style": "^3.7.1", "bcryptjs": "^3.0.2", "dayjs": "^1.11.13", "framer-motion": "^12.23.6", "immer": "^10.1.1", "js-sha256": "^0.11.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "lucide-react": "^0.525.0", "mysql2": "^3.14.2", "nanoid": "^5.1.5", "next": "^15.1.6", "nodemailer": "^7.0.5", "nuqs": "^2.4.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.1", "react-layout-kit": "^2.0.0", "zustand": "^5.0.4"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/lodash-es": "^4.17.12", "@types/node": "^22.16.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-next": "^15.1.6", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}