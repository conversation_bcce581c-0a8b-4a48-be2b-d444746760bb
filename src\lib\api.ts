import { Prompt } from '@/types';
import { Agent, CreateAgentForm } from '@/types/agent';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

// 通用API请求函数
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<{ success: boolean; data?: T; error?: string }> {
  try {
    const url = `${API_BASE_URL}/api${endpoint}`;
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || `HTTP error! status: ${response.status}`);
    }

    return result;
  } catch (error) {
    console.error('API请求失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '请求失败',
    };
  }
}

// 提示词API
export const promptsApi = {
  // 获取所有提示词
  getAll: async (params?: {
    category?: string;
    search?: string;
    tags?: string[];
  }): Promise<{ success: boolean; data?: Prompt[]; error?: string }> => {
    const searchParams = new URLSearchParams();
    
    if (params?.category) {
      searchParams.append('category', params.category);
    }
    if (params?.search) {
      searchParams.append('search', params.search);
    }
    if (params?.tags && params.tags.length > 0) {
      searchParams.append('tags', params.tags.join(','));
    }

    const query = searchParams.toString();
    const endpoint = query ? `/prompts?${query}` : '/prompts';
    
    return apiRequest<Prompt[]>(endpoint);
  },

  // 创建提示词
  create: async (prompt: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt'>): Promise<{
    success: boolean;
    data?: Prompt;
    error?: string;
  }> => {
    return apiRequest<Prompt>('/prompts', {
      method: 'POST',
      body: JSON.stringify(prompt),
    });
  },

  // 更新提示词
  update: async (id: string, updates: Partial<Prompt>): Promise<{
    success: boolean;
    error?: string;
  }> => {
    return apiRequest('/prompts', {
      method: 'PUT',
      body: JSON.stringify({ id, ...updates }),
    });
  },

  // 删除提示词
  delete: async (id: string): Promise<{
    success: boolean;
    error?: string;
  }> => {
    return apiRequest(`/prompts?id=${id}`, {
      method: 'DELETE',
    });
  },
};

// 智能体API
export const agentsApi = {
  // 获取所有智能体
  getAll: async (params?: {
    category?: string;
    search?: string;
    enabled?: boolean;
  }): Promise<{ success: boolean; data?: Agent[]; error?: string }> => {
    const searchParams = new URLSearchParams();
    
    if (params?.category) {
      searchParams.append('category', params.category);
    }
    if (params?.search) {
      searchParams.append('search', params.search);
    }
    if (params?.enabled !== undefined) {
      searchParams.append('enabled', params.enabled.toString());
    }

    const query = searchParams.toString();
    const endpoint = query ? `/agents?${query}` : '/agents';
    
    return apiRequest<Agent[]>(endpoint);
  },

  // 创建智能体
  create: async (agentForm: CreateAgentForm): Promise<{
    success: boolean;
    data?: Agent;
    error?: string;
  }> => {
    return apiRequest<Agent>('/agents', {
      method: 'POST',
      body: JSON.stringify(agentForm),
    });
  },

  // 更新智能体
  update: async (id: string, updates: Partial<Agent>): Promise<{
    success: boolean;
    error?: string;
  }> => {
    // 转换数据格式以匹配API期望的格式
    const apiUpdates = {
      id,
      name: updates.name,
      description: updates.description,
      category: updates.category,
      cozeApiKey: updates.cozeConfig?.apiKey,
      cozeBotId: updates.cozeConfig?.botId,
      cozeUserId: updates.cozeConfig?.userId,
      enabled: updates.enabled,
      usageCount: updates.usageCount,
      trialUsageCount: updates.trialUsageCount,
    };

    return apiRequest('/agents', {
      method: 'PUT',
      body: JSON.stringify(apiUpdates),
    });
  },

  // 删除智能体
  delete: async (id: string): Promise<{
    success: boolean;
    error?: string;
  }> => {
    return apiRequest(`/agents?id=${id}`, {
      method: 'DELETE',
    });
  },

  // 增加使用次数
  incrementUsage: async (id: string): Promise<{
    success: boolean;
    error?: string;
  }> => {
    // 先获取当前智能体信息
    const allAgents = await agentsApi.getAll();
    if (!allAgents.success || !allAgents.data) {
      return { success: false, error: '获取智能体信息失败' };
    }

    const agent = allAgents.data.find(a => a.id === id);
    if (!agent) {
      return { success: false, error: '智能体不存在' };
    }

    // 只更新使用次数
    return agentsApi.update(id, {
      usageCount: (agent.usageCount || 0) + 1,
    });
  },
};

// 智能体使用记录API
export const agentUsageApi = {
  // 记录使用次数
  recordUsage: async (params: {
    userId: string;
    agentId: string;
    sessionId?: string;
    usageType?: 'chat' | 'generation';
    tokensUsed?: number;
    cost?: number;
    usageCount?: number;
    expiryDate?: string | null;
  }): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> => {
    return apiRequest('/agent-usage', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  },

  // 获取使用统计
  getUsageStats: async (params: {
    userId: string;
    agentId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    success: boolean;
    data?: any[];
    error?: string;
  }> => {
    const searchParams = new URLSearchParams();
    searchParams.append('userId', params.userId);

    if (params.agentId) {
      searchParams.append('agentId', params.agentId);
    }
    if (params.startDate) {
      searchParams.append('startDate', params.startDate);
    }
    if (params.endDate) {
      searchParams.append('endDate', params.endDate);
    }

    return apiRequest(`/agent-usage?${searchParams.toString()}`);
  },
};

// 智能体剩余次数 API
export const agentRemainingApi = {
  // 获取剩余次数
  getRemainingCount: async (userId: string, agentId: string): Promise<{
    success: boolean;
    data?: {
      agentId: string;
      agentName: string;
      hasSubscription: boolean;
      subscriptionType?: 'time_based' | 'usage_based';
      remainingCount: number | null;
      expiryDate?: string;
      trialCount: number;
      usedCount?: number;
      status: 'subscribed' | 'trial_available' | 'trial_exhausted';
    };
    error?: string;
  }> => {
    const searchParams = new URLSearchParams({ userId, agentId });
    return apiRequest(`/agent-remaining?${searchParams.toString()}`, {
      method: 'GET',
    });
  },

  // 检查是否可以使用
  checkCanUse: async (userId: string, agentId: string): Promise<{
    success: boolean;
    data?: {
      canUse: boolean;
      reason?: string;
      agentId: string;
      agentName: string;
      hasSubscription: boolean;
      remainingCount: number | null;
      trialCount: number;
      status: string;
    };
    error?: string;
  }> => {
    return apiRequest('/agent-remaining', {
      method: 'POST',
      body: JSON.stringify({ userId, agentId }),
    });
  },
};

// 数据库连接测试
export const testDatabaseConnection = async (): Promise<{
  success: boolean;
  error?: string;
}> => {
  return apiRequest('/test-db');
};

// AI提供商配置API
export const aiProvidersApi = {
  // 获取用户的AI提供商配置
  getConfigs: async (userId: string): Promise<{
    success: boolean;
    data?: any[];
    error?: string;
  }> => {
    return apiRequest(`/ai-providers?userId=${userId}`);
  },

  // 保存AI提供商配置
  saveConfig: async (config: {
    userId: string;
    provider: string;
    enabled: boolean;
    apiKey: string;
    baseUrl?: string;
    defaultModel: string;
    temperature?: number;
    maxTokens?: number;
    extraConfig?: Record<string, any>;
  }): Promise<{
    success: boolean;
    error?: string;
  }> => {
    return apiRequest('/ai-providers', {
      method: 'POST',
      body: JSON.stringify(config),
    });
  },

  // 删除AI提供商配置
  deleteConfig: async (userId: string, provider: string): Promise<{
    success: boolean;
    error?: string;
  }> => {
    return apiRequest(`/ai-providers?userId=${userId}&provider=${provider}`, {
      method: 'DELETE',
    });
  },
};

// 用户设置API
export const userSettingsApi = {
  // 获取用户设置
  getSettings: async (userId: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> => {
    return apiRequest(`/user-settings?userId=${userId}`);
  },

  // 保存用户设置
  saveSettings: async (settings: {
    userId: string;
    currentProvider?: string;
    currentModel?: string;
    theme?: string;
    language?: string;
    fontSize?: string;
    autoSave?: boolean;
    notifications?: boolean;
  }): Promise<{
    success: boolean;
    error?: string;
  }> => {
    return apiRequest('/user-settings', {
      method: 'POST',
      body: JSON.stringify(settings),
    });
  },
};

// 智能体定价 API
export const agentPricingApi = {
  // 获取智能体的定价方案
  getPlans: async (agentId: string): Promise<{
    success: boolean;
    data?: any[];
    error?: string;
  }> => {
    const searchParams = new URLSearchParams({ agentId });
    return apiRequest(`/agent-pricing?${searchParams.toString()}`, {
      method: 'GET',
    });
  },

  // 创建定价方案
  createPlan: async (planData: {
    agentId: string;
    name: string;
    type: 'per_usage' | 'time_based';
    usageCount?: number;
    pricePerUsage?: number;
    durationDays?: number;
    pricePerPeriod?: number;
    currency?: string;
    description?: string;
    isActive?: boolean;
  }): Promise<{
    success: boolean;
    data?: { id: string };
    error?: string;
  }> => {
    return apiRequest('/agent-pricing', {
      method: 'POST',
      body: JSON.stringify(planData),
    });
  },

  // 更新定价方案
  updatePlan: async (id: string, planData: any): Promise<{
    success: boolean;
    error?: string;
  }> => {
    return apiRequest('/agent-pricing', {
      method: 'PUT',
      body: JSON.stringify({ id, ...planData }),
    });
  },
};

// 智能体状态检查 API
export const agentStatusApi = {
  // 检查智能体是否存在且启用
  checkStatus: async (agentId: string): Promise<{
    success: boolean;
    data?: {
      exists: boolean;
      enabled: boolean;
      agentId: string;
      name: string | null;
      reason?: 'agent_not_found' | 'agent_disabled' | null;
    };
    error?: string;
  }> => {
    const searchParams = new URLSearchParams({ agentId });
    return apiRequest(`/agent-status?${searchParams.toString()}`, {
      method: 'GET',
    });
  },
};
