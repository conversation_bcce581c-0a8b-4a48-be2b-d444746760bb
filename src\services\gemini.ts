import { FileInfo, GeminiResponse, ApiResponse } from '@/types';

// Gemini API 配置
interface GeminiConfig {
  apiKey: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  topK?: number;
}

// 默认配置
const DEFAULT_CONFIG: Partial<GeminiConfig> = {
  model: 'gemini-2.5-flash',
  temperature: 0.7,
  maxTokens: 8192,
  topP: 0.8,
  topK: 40,
};

// 支持的文件类型
const SUPPORTED_FILE_TYPES = {
  'image/jpeg': 'image/jpeg',
  'image/png': 'image/png',
  'image/gif': 'image/gif',
  'image/webp': 'image/webp',
  'text/plain': 'text/plain',
  'application/pdf': 'application/pdf',
  'text/csv': 'text/csv',
  'application/json': 'application/json',
};

class GeminiService {
  private config: GeminiConfig;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta';

  constructor(config: GeminiConfig) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // 更新配置
  updateConfig(newConfig: Partial<GeminiConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  // 获取当前配置
  getConfig(): GeminiConfig {
    return { ...this.config };
  }

  // 验证 API Key
  async validateApiKey(apiKey?: string): Promise<boolean> {
    const keyToTest = apiKey || this.config.apiKey;
    if (!keyToTest) return false;

    try {
      const response = await fetch(`${this.baseUrl}/models?key=${keyToTest}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('API Key validation failed:', error);
      return false;
    }
  }

  // 获取可用模型列表
  async getModels(): Promise<ApiResponse<Record<string, unknown>[]>> {
    try {
      const response = await fetch(`${this.baseUrl}/models?key=${this.config.apiKey}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        success: true,
        data: data.models || [],
      };
    } catch (error) {
      console.error('Failed to fetch models:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取模型列表失败',
      };
    }
  }

  // 文件转换为 base64
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // 移除 data:mime/type;base64, 前缀
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  // 处理文件附件
  private async processFiles(files: FileInfo[]): Promise<Record<string, unknown>[]> {
    const processedFiles = [];

    for (const fileInfo of files) {
      if (!SUPPORTED_FILE_TYPES[fileInfo.type as keyof typeof SUPPORTED_FILE_TYPES]) {
        console.warn(`Unsupported file type: ${fileInfo.type}`);
        continue;
      }

      try {
        // 如果有 File 对象，转换为 base64
        if (fileInfo.url && fileInfo.url.startsWith('blob:')) {
          const response = await fetch(fileInfo.url);
          const blob = await response.blob();
          const file = new File([blob], fileInfo.name, { type: fileInfo.type });
          const base64Data = await this.fileToBase64(file);

          processedFiles.push({
            inline_data: {
              mime_type: fileInfo.type,
              data: base64Data,
            },
          });
        } else if (fileInfo.uri) {
          // 如果有 URI，直接使用
          processedFiles.push({
            inline_data: {
              mime_type: fileInfo.type,
              data: fileInfo.uri,
            },
          });
        }
      } catch (error) {
        console.error(`Failed to process file ${fileInfo.name}:`, error);
      }
    }

    return processedFiles;
  }

  // 发送消息到 Gemini API
  async sendMessage(
    message: string,
    files?: FileInfo[],
    conversationHistory?: Array<{ role: string; content: string }>
  ): Promise<ApiResponse<string>> {
    if (!this.config.apiKey) {
      return {
        success: false,
        error: '请先配置 API Key',
      };
    }

    try {
      // 构建消息内容
      const parts: Record<string, unknown>[] = [{ text: message }];

      // 处理文件附件
      if (files && files.length > 0) {
        const processedFiles = await this.processFiles(files);
        parts.push(...processedFiles);
      }

      // 构建请求体
      const requestBody: Record<string, unknown> = {
        contents: [
          {
            parts,
          },
        ],
        generationConfig: {
          temperature: this.config.temperature,
          maxOutputTokens: this.config.maxTokens,
          topP: this.config.topP,
          topK: this.config.topK,
        },
        safetySettings: [
          {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
        ],
      };

      // 如果有对话历史，添加到请求中
      if (conversationHistory && conversationHistory.length > 0) {
        const historyContents = conversationHistory.map(msg => ({
          role: msg.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: msg.content }],
        }));
        
        requestBody.contents = [
          ...historyContents,
          (requestBody.contents as any[])[0],
        ];
      }

      const response = await fetch(
        `${this.baseUrl}/models/${this.config.model}:generateContent?key=${this.config.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      const data: GeminiResponse = await response.json();

      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response generated');
      }

      const generatedText = data.candidates[0]?.content?.parts?.[0]?.text;
      if (!generatedText) {
        throw new Error('Empty response from API');
      }

      return {
        success: true,
        data: generatedText,
      };
    } catch (error) {
      console.error('Gemini API error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成回复失败',
      };
    }
  }
}

// 创建单例实例
let geminiService: GeminiService | null = null;

export const createGeminiService = (config: GeminiConfig): GeminiService => {
  geminiService = new GeminiService(config);
  return geminiService;
};

export const getGeminiService = (): GeminiService | null => {
  return geminiService;
};

export default GeminiService;
