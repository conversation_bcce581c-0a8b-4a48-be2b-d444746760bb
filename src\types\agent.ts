// 智能体相关类型定义

export interface Agent {
  id: string;
  name: string;
  description?: string;
  category: AgentCategory;
  avatar?: string;
  author?: string;
  // Coze 相关配置
  cozeConfig?: {
    apiKey: string;
    botId: string;
    userId: string;
  };
  // 创建和更新时间
  createdAt: string;
  updatedAt: string;
  // 是否启用
  enabled: boolean;
  // 使用次数
  usageCount?: number;
  // 试用次数
  trialUsageCount?: number;
}

export type AgentCategory = 
  | 'general'      // 通用助手
  | 'writing'      // 写作助手
  | 'programming'  // 编程助手
  | 'education'    // 教育助手
  | 'business'     // 商务助手
  | 'creative'     // 创意助手
  | 'analysis'     // 分析助手
  | 'translation'; // 翻译助手

export interface AgentCategoryInfo {
  key: AgentCategory;
  label: string;
  icon: string;
  description: string;
}

// 智能体分类信息
export const AGENT_CATEGORIES: AgentCategoryInfo[] = [
  {
    key: 'general',
    label: '通用助手',
    icon: 'Bot',
    description: '通用对话和问答助手'
  },
  {
    key: 'writing',
    label: '写作助手',
    icon: 'PenTool',
    description: '文案创作和写作辅助'
  },
  {
    key: 'programming',
    label: '编程助手',
    icon: 'Code',
    description: '代码编写和技术支持'
  },
  {
    key: 'education',
    label: '教育助手',
    icon: 'GraduationCap',
    description: '学习辅导和知识问答'
  },
  {
    key: 'business',
    label: '商务助手',
    icon: 'Briefcase',
    description: '商务咨询和工作支持'
  },
  {
    key: 'creative',
    label: '创意助手',
    icon: 'Palette',
    description: '创意设计和灵感激发'
  },
  {
    key: 'analysis',
    label: '分析助手',
    icon: 'BarChart',
    description: '数据分析和报告生成'
  },
  {
    key: 'translation',
    label: '翻译助手',
    icon: 'Languages',
    description: '多语言翻译和本地化'
  }
];

// 创建智能体的表单数据
export interface CreateAgentForm {
  name: string;
  description?: string;
  category: AgentCategory;
  cozeApiKey: string;
  cozeBotId: string;
  cozeUserId: string;
}

// 智能体定价模式
export interface AgentPricingPlan {
  id: string;
  agentId: string;
  name: string;
  type: 'per_usage' | 'time_based';
  // 按次收费字段
  usageCount?: number; // 可用次数
  pricePerUsage?: number; // 每次价格
  // 按时计费字段
  durationDays?: number; // 时长（天）
  pricePerPeriod?: number; // 每期价格
  // 通用字段
  currency: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 用户智能体订阅
export interface UserAgentSubscription {
  id: string;
  userId: string;
  agentId: string;
  pricingPlanId: string;
  // 按次收费相关
  remainingUsage: number; // 剩余使用次数
  totalUsage: number; // 总使用次数
  // 按时计费相关
  startDate?: string; // 开始时间
  endDate?: string; // 结束时间
  // 通用字段
  status: 'active' | 'expired' | 'cancelled';
  purchasePrice: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
}

// 智能体使用记录
export interface AgentUsageLog {
  id: string;
  userId: string;
  agentId: string;
  subscriptionId?: string;
  sessionId?: string;
  usageType: 'chat' | 'generation';
  tokensUsed: number;
  cost: number;
  usageCount: number; // 本次使用次数
  expiryDate?: string; // 截止时间（用于定时定价模式）
  createdAt: string;
}

// 智能体状态
export interface AgentState {
  agents: Agent[];
  selectedCategory: AgentCategory | 'all';
  searchQuery: string;
  loading: boolean;
  error: string | null;
}

// 智能体操作
export interface AgentActions {
  // 获取智能体列表
  fetchAgents: () => Promise<void>;
  // 添加智能体
  addAgent: (agent: CreateAgentForm) => Promise<Agent>;
  // 更新智能体
  updateAgent: (id: string, updates: Partial<Agent>) => Promise<void>;
  // 删除智能体
  deleteAgent: (id: string) => Promise<void>;
  // 设置选中的分类
  setSelectedCategory: (category: AgentCategory | 'all') => void;
  // 设置搜索查询
  setSearchQuery: (query: string) => void;
  // 添加智能体到会话
  addAgentToSession: (agentId: string) => Promise<string>;
  // 增加使用次数
  incrementUsageCount: (agentId: string) => Promise<void>;
}

export type AgentStore = AgentState & AgentActions;
