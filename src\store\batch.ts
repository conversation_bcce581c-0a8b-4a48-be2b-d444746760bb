import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { BatchItem, BatchTemplate, BatchState } from '@/types';
import { getGeminiService } from '@/services/gemini';

interface BatchStore extends BatchState {
  // Actions
  addBatchItem: (item: Omit<BatchItem, 'id' | 'createdAt'>) => void;
  updateBatchItem: (id: string, updates: Partial<BatchItem>) => void;
  removeBatchItem: (id: string) => void;
  clearBatchItems: () => void;
  
  // Templates
  addTemplate: (template: Omit<BatchTemplate, 'id' | 'createdAt'>) => void;
  updateTemplate: (id: string, updates: Partial<BatchTemplate>) => void;
  removeTemplate: (id: string) => void;
  
  // Generation
  generateBatch: () => Promise<void>;
  generateSingle: (id: string) => Promise<void>;
  stopGeneration: () => void;
  pauseGeneration: () => void;
  resumeGeneration: () => Promise<void>;

  // Batch text processing
  addBatchTexts: (prompt: string, texts: string) => void;

  // Export
  exportResults: (format: 'json' | 'csv' | 'txt') => void;
  exportBatchResults: (format: 'json' | 'csv' | 'txt' | 'docx', mode?: 'combined' | 'separate') => void;
  
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setProgress: (progress: { current: number; total: number }) => void;
}

// 生成唯一 ID
const generateId = () => Math.random().toString(36).substr(2, 9);

// 默认模板
const DEFAULT_TEMPLATES: BatchTemplate[] = [
  {
    id: 'marketing-copy',
    name: '营销文案',
    description: '生成产品营销文案',
    prompt: '为以下产品生成一段吸引人的营销文案：{product}',
    variables: ['product'],
    category: 'marketing',
    createdAt: Date.now(),
  },
  {
    id: 'social-media',
    name: '社交媒体',
    description: '生成社交媒体帖子',
    prompt: '为以下主题创建一个引人入胜的社交媒体帖子：{topic}，目标受众：{audience}',
    variables: ['topic', 'audience'],
    category: 'social',
    createdAt: Date.now(),
  },
  {
    id: 'email-subject',
    name: '邮件主题',
    description: '生成邮件主题行',
    prompt: '为以下邮件内容生成5个吸引人的主题行：{content}',
    variables: ['content'],
    category: 'email',
    createdAt: Date.now(),
  },
];

export const useBatchStore = create<BatchStore>()(
  persist(
    (set, get) => ({
      // Initial state
      items: [],
      templates: DEFAULT_TEMPLATES,
      isGenerating: false,
      isPaused: false,
      currentBatchIndex: 0,
      progress: { current: 0, total: 0 },
      loading: false,
      error: null,

      // Batch items management
      addBatchItem: (item) => {
        const newItem: BatchItem = {
          ...item,
          id: generateId(),
          createdAt: Date.now(),
        };
        set((state) => ({
          items: [...state.items, newItem],
        }));
      },

      updateBatchItem: (id, updates) => {
        set((state) => ({
          items: state.items.map(item =>
            item.id === id ? { ...item, ...updates } : item
          ),
        }));
      },

      removeBatchItem: (id) => {
        set((state) => ({
          items: state.items.filter(item => item.id !== id),
        }));
      },

      clearBatchItems: () => {
        set({ items: [] });
      },

      // Templates management
      addTemplate: (template) => {
        const newTemplate: BatchTemplate = {
          ...template,
          id: generateId(),
          createdAt: Date.now(),
        };
        set((state) => ({
          templates: [...state.templates, newTemplate],
        }));
      },

      updateTemplate: (id, updates) => {
        set((state) => ({
          templates: state.templates.map(template =>
            template.id === id ? { ...template, ...updates } : template
          ),
        }));
      },

      removeTemplate: (id) => {
        set((state) => ({
          templates: state.templates.filter(template => template.id !== id),
        }));
      },

      // Generation
      generateBatch: async () => {
        const { items } = get();
        const pendingItems = items.filter(item => item.status === 'pending');
        
        if (pendingItems.length === 0) {
          set({ error: '没有待生成的项目' });
          return;
        }

        const geminiService = getGeminiService();
        if (!geminiService) {
          set({ error: 'Gemini 服务未初始化，请先配置 API Key' });
          return;
        }

        set({ 
          isGenerating: true, 
          error: null,
          progress: { current: 0, total: pendingItems.length }
        });

        let completed = 0;

        for (const item of pendingItems) {
          if (!get().isGenerating) break; // 检查是否被停止

          // 更新项目状态为生成中
          get().updateBatchItem(item.id, { 
            status: 'generating',
            startTime: Date.now()
          });

          try {
            // 构建完整的提示词
            let finalPrompt = item.prompt;
            if (item.variables) {
              Object.entries(item.variables).forEach(([key, value]) => {
                finalPrompt = finalPrompt.replace(new RegExp(`{${key}}`, 'g'), value);
              });
            }

            // 调用 API 生成内容
            const result = await geminiService.sendMessage(finalPrompt);

            if (result.success && result.data) {
              get().updateBatchItem(item.id, {
                status: 'completed',
                result: result.data,
                endTime: Date.now(),
              });
            } else {
              get().updateBatchItem(item.id, {
                status: 'failed',
                error: result.error || '生成失败',
                endTime: Date.now(),
              });
            }
          } catch (error) {
            get().updateBatchItem(item.id, {
              status: 'failed',
              error: error instanceof Error ? error.message : '生成失败',
              endTime: Date.now(),
            });
          }

          completed++;
          set((state) => ({
            progress: { current: completed, total: state.progress.total }
          }));

          // 添加延迟以避免 API 限制
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        set({ isGenerating: false });
      },

      generateSingle: async (id) => {
        const { items } = get();
        const item = items.find(item => item.id === id);
        
        if (!item) {
          set({ error: '项目不存在' });
          return;
        }

        const geminiService = getGeminiService();
        if (!geminiService) {
          set({ error: 'Gemini 服务未初始化，请先配置 API Key' });
          return;
        }

        // 更新项目状态
        get().updateBatchItem(id, { 
          status: 'generating',
          startTime: Date.now()
        });

        try {
          // 构建完整的提示词
          let finalPrompt = item.prompt;
          if (item.variables) {
            Object.entries(item.variables).forEach(([key, value]) => {
              finalPrompt = finalPrompt.replace(new RegExp(`{${key}}`, 'g'), value);
            });
          }

          // 调用 API 生成内容
          const result = await geminiService.sendMessage(finalPrompt);

          if (result.success && result.data) {
            get().updateBatchItem(id, {
              status: 'completed',
              result: result.data,
              endTime: Date.now(),
            });
          } else {
            get().updateBatchItem(id, {
              status: 'failed',
              error: result.error || '生成失败',
              endTime: Date.now(),
            });
          }
        } catch (error) {
          get().updateBatchItem(id, {
            status: 'failed',
            error: error instanceof Error ? error.message : '生成失败',
            endTime: Date.now(),
          });
        }
      },

      stopGeneration: () => {
        set({ isGenerating: false });
      },

      // Batch text processing
      addBatchTexts: (prompt, texts) => {
        const lines = texts.split('\n').filter(line => line.trim());
        const newItems = lines.map(line => ({
          input: line.trim(),
          prompt: prompt || '',
          status: 'pending' as const,
          result: '',
          error: undefined,
        }));

        // 清除现有项目并添加新项目
        set({ items: [] });
        newItems.forEach(item => get().addBatchItem(item));
      },

      // Export
      exportResults: (format) => {
        const { items } = get();
        const completedItems = items.filter(item => item.status === 'completed');
        
        if (completedItems.length === 0) {
          set({ error: '没有已完成的结果可导出' });
          return;
        }

        let content = '';
        let filename = '';
        let mimeType = '';

        switch (format) {
          case 'json':
            content = JSON.stringify(completedItems, null, 2);
            filename = `batch-results-${new Date().toISOString().split('T')[0]}.json`;
            mimeType = 'application/json';
            break;
          
          case 'csv':
            const headers = ['ID', '提示词', '结果', '创建时间', '完成时间'];
            const rows = completedItems.map(item => [
              item.id,
              item.prompt.replace(/"/g, '""'),
              item.result?.replace(/"/g, '""') || '',
              new Date(item.createdAt).toLocaleString(),
              item.endTime ? new Date(item.endTime).toLocaleString() : '',
            ]);
            content = [headers, ...rows].map(row => 
              row.map(cell => `"${cell}"`).join(',')
            ).join('\n');
            filename = `batch-results-${new Date().toISOString().split('T')[0]}.csv`;
            mimeType = 'text/csv';
            break;
          
          case 'txt':
            content = completedItems.map((item, index) => 
              `=== 结果 ${index + 1} ===\n提示词: ${item.prompt}\n\n结果:\n${item.result}\n\n`
            ).join('');
            filename = `batch-results-${new Date().toISOString().split('T')[0]}.txt`;
            mimeType = 'text/plain';
            break;
        }

        // 下载文件
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
      },

      exportBatchResults: (format, mode = 'combined') => {
        const { items } = get();
        const completedItems = items.filter(item => item.status === 'completed');

        if (completedItems.length === 0) {
          console.warn('没有已完成的结果可导出');
          return;
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

        if (mode === 'combined') {
          // 合并导出
          let content = '';
          if (format === 'txt') {
            content = completedItems.map((item, index) =>
              `${index + 1}. ${item.input || item.prompt}\n${item.result}\n`
            ).join('\n');
          }

          const blob = new Blob([content], { type: 'text/plain' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `batch-results-${timestamp}.${format}`;
          a.click();
          URL.revokeObjectURL(url);
        }
      },

      // State management
      setLoading: (loading) => {
        set({ loading });
      },

      setError: (error) => {
        set({ error });
      },

      setProgress: (progress) => {
        set({ progress });
      },

      pauseGeneration: () => {
        set({ isPaused: true });
      },

      resumeGeneration: async () => {
        set({ isPaused: false });
      },
    }),
    {
      name: 'batch-storage',
      partialize: (state) => ({
        items: state.items,
        templates: state.templates,
      }),
    }
  )
);
