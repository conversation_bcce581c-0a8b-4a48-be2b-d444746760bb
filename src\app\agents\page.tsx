'use client';

import React, { useState, useEffect } from 'react';
import { createStyles } from 'antd-style';
import { Flexbox } from 'react-layout-kit';
import { App } from 'antd';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/layout/AppLayout';
import AgentCategory from '@/components/agents/AgentCategory';
import AgentSearch from '@/components/agents/AgentSearch';
import AgentList from '@/components/agents/AgentList';
import AddAgentModal from '@/components/agents/AddAgentModal';
import { useAgentStore } from '@/store/agent';
import { useAuthStore } from '@/store/auth';

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    height: 100vh;
    display: flex;
    background: ${token.colorBgLayout};
  `,
  sidebar: css`
    width: 280px;
    background: ${token.colorBgContainer};
    border-right: 1px solid ${token.colorBorderSecondary};
    display: flex;
    flex-direction: column;
    height: 100%;
  `,
  sidebarHeader: css`
    padding: 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    
    .title {
      font-size: 18px;
      font-weight: 600;
      color: ${token.colorText};
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .description {
      font-size: 14px;
      color: ${token.colorTextSecondary};
      margin: 4px 0 0 0;
    }
  `,
  sidebarContent: css`
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  `,
  mainContent: css`
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  `,
  contentHeader: css`
    flex-shrink: 0;
  `,
  contentBody: css`
    flex: 1;
    overflow: hidden;
  `,
}));

const AgentsPage: React.FC = () => {
  const { styles } = useStyles();
  const router = useRouter();
  const { message } = App.useApp();
  const [showAddModal, setShowAddModal] = useState(false);
  const { fetchAgents, agents } = useAgentStore();
  const { isLoggedIn } = useAuthStore();

  // 检查登录状态
  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
  }, [isLoggedIn, router]);

  // 初始化数据
  useEffect(() => {
    if (isLoggedIn) {
      fetchAgents();
    }
  }, [isLoggedIn, fetchAgents]);

  // 处理添加智能体到会话
  const handleAddToSession = (agentId: string) => {
    const agent = agents.find(a => a.id === agentId);
    if (agent) {
      message.success(`已将 ${agent.name} 添加到新会话`);
      // 跳转到聊天页面
      router.push('/chat');
    }
  };

  if (!isLoggedIn) {
    return null;
  }

  return (
    <AppLayout>
      <div className={styles.container}>
        {/* 左侧边栏 */}
        <div className={styles.sidebar}>
          <div className={styles.sidebarHeader}>
            <h2 className="title">
              <span>🤖</span>
              智能体
            </h2>
            <p className="description">
              管理和使用各种智能体助手
            </p>
          </div>
          
          <div className={styles.sidebarContent}>
            <AgentCategory />
          </div>
        </div>

        {/* 主内容区域 */}
        <div className={styles.mainContent}>
          <div className={styles.contentHeader}>
            <AgentSearch />
          </div>
          
          <div className={styles.contentBody}>
            <AgentList onAddToSession={handleAddToSession} />
          </div>
        </div>

        {/* 添加智能体模态框 */}
        <AddAgentModal
          visible={showAddModal}
          onClose={() => setShowAddModal(false)}
        />
      </div>
    </AppLayout>
  );
};

export default AgentsPage;
