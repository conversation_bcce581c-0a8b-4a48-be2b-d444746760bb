'use client';

import { Avatar } from '@lobehub/ui';
import { memo } from 'react';
import { Dropdown, Modal, message } from 'antd';
import { LogOut, User, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';

const MainSideBarAvatar = memo(() => {
  const { user, logout } = useAuthStore();
  const router = useRouter();

  const handleLogout = () => {
    Modal.confirm({
      title: '退出登录',
      content: '确定要退出登录吗？',
      onOk: () => {
        logout();
        message.success('已退出登录');
        router.push('/login');
      },
      okText: '确认',
      cancelText: '取消',
    });
  };

  const menuItems = [
    {
      key: 'profile',
      label: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <User size={16} />
          <span>个人信息</span>
        </div>
      ),
      onClick: () => {
        message.info('个人信息功能开发中');
      },
    },
    {
      key: 'settings',
      label: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Settings size={16} />
          <span>设置</span>
        </div>
      ),
      onClick: () => {
        router.push('/settings');
      },
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#ff4d4f' }}>
          <LogOut size={16} />
          <span>退出登录</span>
        </div>
      ),
      onClick: handleLogout,
    },
  ];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      trigger={['click']}
      placement="bottomLeft"
      arrow
    >
      <div style={{ cursor: 'pointer' }}>
        <Avatar
          avatar={user?.avatar || '/favicon.ico'}
          title={user?.username || '用户'}
          size={40}
        />
      </div>
    </Dropdown>
  );
});

MainSideBarAvatar.displayName = 'MainSideBarAvatar';

export default MainSideBarAvatar;
