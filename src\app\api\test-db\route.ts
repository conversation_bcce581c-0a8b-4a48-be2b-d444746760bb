import { NextResponse } from 'next/server';
import { testConnection } from '@/lib/db';

// GET - 测试数据库连接
export async function GET() {
  try {
    const isConnected = await testConnection();
    
    if (isConnected) {
      return NextResponse.json({
        success: true,
        message: '数据库连接成功',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: '数据库连接失败',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('数据库连接测试失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '数据库连接测试失败',
      },
      { status: 500 }
    );
  }
}
