import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import ClientProviders from '@/components/providers/ClientProviders'
import { initializeGeminiService } from '@/store/settings'
import './globals.css'

// 初始化 Gemini 服务
if (typeof window !== 'undefined') {
  initializeGeminiService();
}

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '微甜 Ai Studio',
  description: '基于 Gemini API 的智能对话和批量文案生成工具',
  keywords: ['Gemini', 'AI', '智能对话', '批量生成', 'Next.js'],
  authors: [{ name: '微甜Ai Studio' }],
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 尽早抑制 antd 兼容性警告
              (function() {
                const originalWarn = console.warn;
                const originalError = console.error;

                console.warn = function(...args) {
                  const message = args[0];
                  if (typeof message === 'string' && (
                    message.includes('antd v5 support React is 16') ||
                    message.includes('compatible') ||
                    message.includes('https://u.ant.design/v5-for-19') ||
                    message.includes('antd: compatible')
                  )) {
                    return;
                  }
                  originalWarn.apply(console, args);
                };

                console.error = function(...args) {
                  const message = args[0];
                  if (typeof message === 'string' && (
                    message.includes('antd v5 support React is 16') ||
                    message.includes('compatible') ||
                    message.includes('https://u.ant.design/v5-for-19') ||
                    message.includes('antd: compatible')
                  )) {
                    return;
                  }
                  originalError.apply(console, args);
                };
              })();
            `,
          }}
        />
        <link rel="icon" href="/favicon.ico" />
        <link
          href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://fonts.googleapis.com/icon?family=Material+Icons"
          rel="stylesheet"
        />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  )
}